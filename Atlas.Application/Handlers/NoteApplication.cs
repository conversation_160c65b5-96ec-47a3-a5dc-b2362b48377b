using Atlas.Application.Interfaces;
using Atlas.Application.Models.Note;
using Atlas.Business.Core.Note;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.Note;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Models.Responses;
using Atlas.CrossCutting.Services;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using MapsterMapper;
using System.Net;
using System.Security;
using System.Security.Principal;

namespace Atlas.Application.Handlers
{
    public class NoteApplication
    (
        IStructuredLogger logger,
        IMapper mapper,
        IPrincipal principal,
        INoteService noteService,
        IRequestContextService requestContext
    ) : INoteApplication
    {
        private readonly AuthUtil _authUtil = new AuthUtil(principal);
        private readonly IStructuredLogger _logger = logger;
        private readonly IMapper _mapper = mapper;
        private readonly INoteService _noteService = noteService;
        private readonly IRequestContextService _requestContext = requestContext;

        public async Task<PagedResult<NoteListItem>> GetNoteListAsync(NoteListGetRequest request)
        {
            try
            {
                var workgroups = (request.workgroupId.HasValue && request.workgroupId.Value > 0)
                    ? new[] { request.workgroupId.Value } : Array.Empty<int>();

                var filters = new ContentRequestFilter
                {
                    workgroups = workgroups,
                    createUserId = null,
                    status = null,
                    multiStatus = null,
                    pageNumber = request.pageNumber,
                    pageSize = request.pageSize,
                    orderBy = request.orderBy ?? true
                };

                var (noteViewModels, total) = await _noteService.GetListAsync(filters);

                // Usar Mapster para mapeamento
                var noteListItems = _mapper.Map<List<NoteListItem>>(noteViewModels);

                return new PagedResult<NoteListItem>(
                    noteListItems,
                    request.pageNumber,
                    request.pageSize,
                    total
                );
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while retrieving note list.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while creating task.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error retrieving note list.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<NoteDetailGetResponse> GetNoteByContentUuidAsync(NoteDetailGetRequest request)
        {
            try
            {
                var noteDetail = await _noteService.GetNoteByContentUuidAsync(request.ContentUuid);

                if (noteDetail == null)
                {
                    throw new HttpCustomException(GeneralErrorCodes.NotFound, HttpStatusCode.NotFound);
                }

                return _mapper.Map<NoteDetailGetResponse>(noteDetail);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while retrieving note detail.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (HttpCustomException)
            {
                throw; // Re-throw HttpCustomException as is
            }
            catch (Exception ex)
            {
                _logger.LogError("Error retrieving note detail.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<CreateNoteResponse> CreateNoteAsync(CreateNoteDto createNoteDto)
        {
            try
            {
                List<Note> listNote = new List<Note>();
                listNote.Add(new Note() { text = createNoteDto.Text});

                Content content = new Content()
                {
                    workgroupId = createNoteDto.WorkgroupId,
                    type = ContentTypes.Note,
                    Note = listNote,
                    title = createNoteDto.Title,
                };

                var userAgent = _requestContext.UserAgent;

                Guid result = await _noteService.CreateNoteAsync(content, userAgent, permissions: null, createUser: null, activities: null, subscribers: null);

                if (result == Guid.Empty)
                {
                    _logger.LogError("Unexpected error while creating note. RESTRICTED");
                    throw new HttpCustomException(TaskErrorCodes.TaskCreateError, HttpStatusCode.InternalServerError);
                }
                else
                {
                    return new CreateNoteResponse { Id = result };
                }
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while creating task.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while creating task.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while creating task.", ex);
                throw new HttpCustomException(TaskErrorCodes.TaskCreateError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<object> UpdateNoteAsync(UpdateNoteRequest updateNoteRequest)
        {
            Content content = _mapper.Map<Content>(updateNoteRequest);
            Note note = new Note { text = updateNoteRequest.note.text };

            List<Note> notes = [note];
            content.Note = notes;

            return await _noteService.UpdateNoteAsync(content, updateNoteRequest.individuallyUpdated ?? true);
        }

        public async Task<bool> DeleteNoteAsync(Guid contentUuid, string userAgent, bool individuallyDeleted = true)
        {
            try
            {
                return await _noteService.DeleteAsync(contentUuid, userAgent);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Unauthorized delete note.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation during delete note.", ex);
                throw new HttpCustomException(TaskErrorCodes.TaskDeleteError, HttpStatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error deleting note.", ex);
                throw new HttpCustomException(TaskErrorCodes.TaskDeleteError, HttpStatusCode.InternalServerError);
            }
        }
    }
}
